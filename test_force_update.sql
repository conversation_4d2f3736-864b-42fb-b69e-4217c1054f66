-- Test wymuszenia aktualizacji

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> obecne wartości
SELECT `nip`, `latitude`, `longitude` 
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` = '8711736596';

-- 2. <PERSON><PERSON><PERSON><PERSON> aktualizację przez dodanie komentarza lub małej zmiany
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = '21.0788854', 
    `latitude` = '50.3134211'
WHERE `nip` = '8711736596';

-- 3. Sprawdź ROW_COUNT
SELECT ROW_COUNT() as affected_rows;

-- 4. <PERSON> z lekko inną wartością (dodaj 0.0000001)
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = '21.0788855', 
    `latitude` = '50.3134212'
WHERE `nip` = '8711736596';

SELECT ROW_COUNT() as affected_rows_2;

-- 5. <PERSON><PERSON><PERSON><PERSON><PERSON> czy się zmieniło
SELECT `nip`, `latitude`, `longitude` 
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` = '8711736596';
