-- S<PERSON><PERSON><PERSON><PERSON><PERSON> danych w tabeli stg_geodir_gd_place_detail

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> strukturę tabeli
DESCRIBE stg_geodir_gd_place_detail;

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> kilka przykładowych rekordów
SELECT nip, longitude, latitude 
FROM stg_geodir_gd_place_detail 
LIMIT 10;

-- 3. <PERSON><PERSON><PERSON><PERSON><PERSON> czy istnieją rekordy z pierwszymi NIP-ami z pliku update_coordinates.sql
SELECT nip, longitude, latitude 
FROM stg_geodir_gd_place_detail 
WHERE nip IN ('8711736596', '8711774094', '8711150241', '8711771718', '8711005846');

-- 4. Sprawdź typ danych pola nip
SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'stg_geodir_gd_place_detail' 
AND COLUMN_NAME = 'nip';

-- 5. <PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON> są jakieś rekordy z pustymi współrzędnymi
SELECT COUNT(*) as total_records,
       COUNT(CASE WHEN longitude IS NULL THEN 1 END) as null_longitude,
       COUNT(CASE WHEN latitude IS NULL THEN 1 END) as null_latitude,
       COUNT(CASE WHEN longitude IS NOT NULL AND latitude IS NOT NULL THEN 1 END) as with_coordinates
FROM stg_geodir_gd_place_detail;

-- 6. Sprawdź czy są jakieś rekordy z NIP-ami podobnymi do tych z pliku
SELECT nip, longitude, latitude 
FROM stg_geodir_gd_place_detail 
WHERE nip LIKE '8711%' 
LIMIT 5;

-- 7. Test pojedynczego UPDATE (skomentowany - odkomentuj jeśli chcesz przetestować)
-- UPDATE stg_geodir_gd_place_detail SET longitude = 21.0788854, latitude = 50.3134211 WHERE nip = '8711736596';
-- SELECT ROW_COUNT() as affected_rows;
