-- Spraw<PERSON><PERSON><PERSON> czy dane istnieją w tabeli

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> ile rekordów jest w tabeli
SELECT COUNT(*) as total_records FROM stg_geodir_gd_place_detail;

-- 2. Sprawdź kilka przykładowych rekordów z NIP-ami
SELECT post_id, nip, latitude, longitude, city 
FROM stg_geodir_gd_place_detail 
WHERE nip IS NOT NULL 
LIMIT 10;

-- 3. <PERSON><PERSON><PERSON><PERSON><PERSON> czy istnieją konkretne NIP-y z naszego pliku
SELECT nip, latitude, longitude, city
FROM stg_geodir_gd_place_detail 
WHERE nip IN ('8711736596', '8711774094', '8711150241', '8711771718', '8711005846');

-- 4. <PERSON>prawd<PERSON> czy są jakieś NIP-y zaczynające się od 8711
SELECT COUNT(*) as count_8711
FROM stg_geodir_gd_place_detail 
WHERE nip LIKE '8711%';

-- 5. <PERSON><PERSON><PERSON><PERSON><PERSON> przykładowe NIP-y w bazie
SELECT DISTINCT nip 
FROM stg_geodir_gd_place_detail 
WHERE nip IS NOT NULL 
LIMIT 10;

-- 6. Sprawdź czy są rekordy z pustymi współrzędnymi
SELECT COUNT(*) as empty_coordinates
FROM stg_geodir_gd_place_detail 
WHERE (latitude IS NULL OR latitude = '' OR latitude = '0') 
   AND (longitude IS NULL OR longitude = '' OR longitude = '0');

-- 7. Test pojedynczego UPDATE (sprawdź czy rekord istnieje przed aktualizacją)
SELECT 'Before UPDATE:' as status, nip, latitude, longitude 
FROM stg_geodir_gd_place_detail 
WHERE nip = '8711736596';

-- Jeśli powyższe zapytanie zwróci rekord, możesz przetestować UPDATE:
-- UPDATE stg_geodir_gd_place_detail 
-- SET longitude = '21.0788854', latitude = '50.3134211' 
-- WHERE nip = '8711736596';

-- SELECT 'After UPDATE:' as status, nip, latitude, longitude 
-- FROM stg_geodir_gd_place_detail 
-- WHERE nip = '8711736596';
