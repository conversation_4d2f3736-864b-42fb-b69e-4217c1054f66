#!/usr/bin/env python3
# translate_po.py

import sys
import os
import polib
import deepl
import time
# Tutaj wpisz swój klucz API DeepL
DEEPL_API_KEY = "9d93176f-3160-4d84-974d-df3f47b73e2f:fx"

def check_requirements():
    try:
        import polib
        import deepl
    except ImportError:
        print("Instalowanie wymaganych bibliotek...")
        os.system("pip install polib deepl")
        print("Biblioteki zostały zainstalowane.")

def translate_po_file(input_file, output_file):
    try:
        # Inicjalizacja tłumacza DeepL
        translator = deepl.Translator(DEEPL_API_KEY)
        
        # Sprawdzenie czy plik istnieje
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Nie znaleziono pliku: {input_file}")
            
        # Wczytanie pliku .po
        po = polib.pofile(input_file)
        
        total_entries = len(po)
        translated_count = 0
        
        print(f"Rozpoczęto tłumaczenie {total_entries} wpisów...")
        
        # Iteracja przez wszystkie wpisy
        for i, entry in enumerate(po, 1):
            if entry.msgid != "":
                try:
                    # Przygotowanie kontekstu
                    context = f"Context: {entry.msgctxt}\n" if entry.msgctxt else ""
                    
                    # Tłumaczenie tekstu
                    result = translator.translate_text(
                        entry.msgid,
                        source_lang="EN",
                        target_lang="PL",
                        preserve_formatting=True,
                        tag_handling="xml"
                    )
                    
                    entry.msgstr = result.text
                    translated_count += 1
                    
                    # Wyświetlanie postępu i tłumaczenia
                    print(f"\n[{i}/{total_entries}] Tłumaczenie:")
                    if context:
                        print(f"Kontekst: {context}")
                    print(f"Oryginał (EN): {entry.msgid}")
                    print(f"Tłumaczenie (PL): {result.text}")
                    print("-" * 50)
                    
                    # Dodanie małego opóźnienia
                    time.sleep(0.5)
                    
                except Exception as e:
                    print(f"Błąd podczas tłumaczenia '{entry.msgid}': {str(e)}")
                    print("Próba ponownego tłumaczenia za 5 sekund...")
                    time.sleep(5)
                    try:
                        result = translator.translate_text(
                            entry.msgid,
                            source_lang="EN",
                            target_lang="PL",
                            preserve_formatting=True
                        )
                        entry.msgstr = result.text
                        translated_count += 1
                    except Exception as e:
                        print(f"Druga próba nieudana: {str(e)}")
        
        # Zapisanie przetłumaczonego pliku
        po.save(output_file)
        print(f"\nZakończono tłumaczenie!")
        print(f"Przetłumaczono {translated_count} z {total_entries} wpisów")
        print(f"Plik zapisano jako: {output_file}")
        
        # Wyświetl statystyki użycia API
        usage = translator.get_usage()
        print(f"\nStatystyki użycia DeepL API:")
        print(f"Wykorzystano znaków: {usage.character.count}/{usage.character.limit}")
        
    except Exception as e:
        print(f"Wystąpił błąd: {str(e)}")

def main():
    # Sprawdzenie wymaganych bibliotek
    check_requirements()
    
    # Sprawdzenie argumentów
    if len(sys.argv) != 3:
        print("Użycie: python translate_po.py plik_wejściowy.po plik_wyjściowy.po")
        print("Przykład: python translate_po.py buddyboss-de_DE_formal.po buddyboss-pl_PL.po")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    # Rozpoczęcie tłumaczenia
    translate_po_file(input_file, output_file)

if __name__ == "__main__":
    main()