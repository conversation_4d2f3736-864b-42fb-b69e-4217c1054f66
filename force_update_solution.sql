-- Rozwiązanie: W<PERSON><PERSON><PERSON> aktualizację przez z<PERSON> wartości

-- Opcja 1: <PERSON><PERSON><PERSON><PERSON><PERSON> ustaw na NULL, potem na właściwą wartość
UPDATE `stg_geodir_gd_place_detail` SET `longitude` = NULL WHERE `nip` = '8711736596';
UPDATE `stg_geodir_gd_place_detail` SET `longitude` = '21.0788854', `latitude` = '50.3134211' WHERE `nip` = '8711736596';

-- Opcja 2: Użyj CASE WHEN aby zawsze była zmiana
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = CASE WHEN `longitude` = '21.0788854' THEN '21.0788854' ELSE '21.0788854' END,
    `latitude` = CASE WHEN `latitude` = '50.3134211' THEN '50.3134211' ELSE '50.3134211' END
WHERE `nip` = '8711736596';

-- Opcja 3: <PERSON><PERSON><PERSON> do<PERSON> kolumnę do zmiany
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = '21.0788854', 
    `latitude` = '50.3134211',
    `mapzoom` = `mapzoom`  -- Wymusza zmianę rekordu
WHERE `nip` = '8711736596';
