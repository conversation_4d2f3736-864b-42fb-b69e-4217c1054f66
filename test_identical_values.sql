-- Test czy problem to identy<PERSON>ne wartości

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> obecne wartości dla pierwszego NIP-a
SELECT `nip`, `latitude`, `longitude` 
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` = '8711736596';

nip
latitude
longitude
Full texts
8711736596
50.3134211
21.0788854
Delete Delete
Copy Copy
Edit Edit


-- 2. Spróbuj UPDATE z lekko inną wartością
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = '21.0788855', `latitude` = '50.3134212' 
WHERE `nip` = '8711736596';

 1 row affected. (Wykonanie zapytania trwało 0.0166 sekund(y).)
SELECT ROW_COUNT() as affected_rows_different_values;

-- 3. Sprawd<PERSON> czy się zmieniło
SELECT `nip`, `latitude`, `longitude` 
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` = '8711736596';

nip
latitude
longitude
Full texts
8711736596
50.3134212
21.0788855
Delete Delete
Copy Copy
Edit Edit



-- 4. <PERSON>raz spróbuj z dokładnie tymi samymi wartościami co wcześniej
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = '21.0788854', `latitude` = '50.3134211' 
WHERE `nip` = '8711736596';

 1 row affected. (Wykonanie zapytania trwało 0.0075 sekund(y).)
UPDATE `stg_geodir_gd_place_detail` SET `longitude` = '21.0788854', `latitude` = '50.3134211' WHERE `nip` = '8711736596';

SELECT ROW_COUNT() as affected_rows_same_values;
