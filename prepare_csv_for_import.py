import pandas as pd
import csv
import sys
import logging
from datetime import datetime

# --- KONFIGURACJA ---

# Ścieżka do pliku wejściowego, kt<PERSON><PERSON> ch<PERSON>.
INPUT_FILE = '/Users/<USER>/Downloads/KRS_ Powiat dąbrowski - NEW IMPORT _ (4).csv'

# Ścieżka do pliku wyjściowego, który będzie gotowy do importu.
OUTPUT_FILE = '/Users/<USER>/Downloads/KRS_CLEANED_FOR_IMPORT.csv'

# Lista kolumn, w których skrypt ma usunąć znaki nowej linii.
COLUMNS_TO_CLEAN = ['pozostale_kody_pkd', 'post_content']

# Separator, który zastąpi znaki nowej linii w czyszczonych kolumnach.
CLEANING_SEPARATOR = ' | '

# --- KONIEC KONFIGURACJI ---

def setup_logging():
    """Konfiguruje system logowania, aby wyświ<PERSON><PERSON>ć informacje w konsoli i zapisywać je do pliku."""
    log_filename = f"cleanup_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.FileHandler(log_filename, encoding='utf-8'), logging.StreamHandler(sys.stdout)]
    )
    logging.info(f"Logi będą zapisywane w pliku: {log_filename}")

def clean_csv_for_import(input_file, output_file, columns_to_clean, separator):
    """
    Czyści plik CSV, aby był kompatybilny z importerami WordPress/GeoDirectory.
    - Zastępuje znaki nowej linii w wybranych kolumnach separatorem.
    - Zapisuje plik w bezpiecznym formacie (UTF-8 z BOM), cytując wszystkie pola.
    """
    try:
        logging.info(f"Wczytywanie pliku: {input_file}")
        df = pd.read_csv(input_file, quotechar='"', engine='python', lineterminator='\n')
        logging.info(f"Wczytano {len(df)} wierszy.")

        for column in columns_to_clean:
            if column in df.columns:
                logging.info(f"Czyszczenie kolumny: '{column}'...")
                # Zamienia znaki nowej linii i usuwa zbędne spacje
                df[column] = df[column].str.replace(r'[\r\n]+', separator, regex=True).str.strip()
            else:
                logging.warning(f"Kolumna '{column}' do czyszczenia nie została znaleziona w pliku.")

        logging.info(f"Zapisywanie oczyszczonego pliku do: {output_file}")
        df.to_csv(output_file, index=False, quoting=csv.QUOTE_ALL, encoding='utf-8-sig')
        
        logging.info("\n--- GOTOWE ---")
        logging.info(f"Plik '{output_file}' został pomyślnie utworzony i jest gotowy do importu.")

    except Exception as e:
        logging.error(f"Wystąpił błąd: {e}", exc_info=True)

if __name__ == "__main__":
    setup_logging()
    clean_csv_for_import(INPUT_FILE, OUTPUT_FILE, COLUMNS_TO_CLEAN, CLEANING_SEPARATOR)