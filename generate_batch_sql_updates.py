#!/usr/bin/env python3
"""
Skrypt do generowania wsadowych zapytań SQL UPDATE dla lepszej wydajności.
Używa konstrukcji CASE WHEN dla aktualizacji wielu rekordów jednocześnie.
"""

import pandas as pd
import sys

def generate_batch_sql_updates(csv_file, output_file=None, batch_size=100):
    """
    Generuje wsadowe zapytania SQL UPDATE na podstawie pliku CSV.
    
    Args:
        csv_file: Ścieżka do pliku CSV z wynikami geokodowania
        output_file: Ścieżka do pliku wyjściowego SQL (opcjonalne)
        batch_size: Liczba rekordów w jednym zapytaniu UPDATE
    """
    
    try:
        # Wczytaj plik CSV
        print(f"Wczytywanie pliku: {csv_file}")
        df = pd.read_csv(csv_file, sep=';')
        print(f"Wczytano {len(df)} wierszy")
        
        # Sprawdź wymagane kolumny
        required_columns = ['id', 'latitude', 'longitude']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"BŁĄD: Brakuje kolumn: {missing_columns}")
            return
        
        # Filtruj tylko wiersze z prawidłowymi współrzędnymi
        valid_coords = df.dropna(subset=['latitude', 'longitude'])
        print(f"Znaleziono {len(valid_coords)} wierszy z prawidłowymi współrzędnymi")
        
        if len(valid_coords) == 0:
            print("BŁĄD: Brak wierszy z prawidłowymi współrzędnymi")
            return
        
        # Generuj zapytania SQL UPDATE
        sql_statements = []
        
        # Nagłówek
        sql_statements.append("-- Wsadowe zapytania UPDATE do aktualizacji współrzędnych geograficznych")
        sql_statements.append("-- Wygenerowane automatycznie na podstawie wyników geokodowania")
        sql_statements.append(f"-- Źródło: {csv_file}")
        sql_statements.append(f"-- Data: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sql_statements.append(f"-- Liczba rekordów do aktualizacji: {len(valid_coords)}")
        sql_statements.append(f"-- Rozmiar wsadu: {batch_size} rekordów")
        sql_statements.append("")
        
        # Rozpocznij transakcję
        sql_statements.append("START TRANSACTION;")
        sql_statements.append("")
        
        # Podziel dane na wsady
        total_batches = (len(valid_coords) + batch_size - 1) // batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min((batch_num + 1) * batch_size, len(valid_coords))
            batch_data = valid_coords.iloc[start_idx:end_idx]
            
            sql_statements.append(f"-- Wsad {batch_num + 1}/{total_batches} (rekordy {start_idx + 1}-{end_idx})")
            
            # Generuj zapytanie UPDATE z CASE WHEN
            nip_list = []
            longitude_cases = []
            latitude_cases = []
            
            for _, row in batch_data.iterrows():
                nip = row['id']
                latitude = row['latitude']
                longitude = row['longitude']
                
                # Sprawdź, czy wartości są liczbami
                try:
                    lat_float = float(latitude)
                    lon_float = float(longitude)
                except (ValueError, TypeError):
                    print(f"OSTRZEŻENIE: Nieprawidłowe współrzędne dla NIP {nip}: lat={latitude}, lon={longitude}")
                    continue
                
                nip_list.append(f"'{nip}'")
                longitude_cases.append(f"    WHEN `nip` = '{nip}' THEN '{lon_float}'")
                latitude_cases.append(f"    WHEN `nip` = '{nip}' THEN '{lat_float}'")
            
            if nip_list:  # Jeśli są prawidłowe dane w tym wsadzie
                # Zapytanie UPDATE z CASE WHEN
                update_sql = f"""UPDATE `stg_geodir_gd_place_detail` SET
  `longitude` = CASE
{chr(10).join(longitude_cases)}
  END,
  `latitude` = CASE
{chr(10).join(latitude_cases)}
  END
WHERE `nip` IN ({', '.join(nip_list)});"""
                
                sql_statements.append(update_sql)
                sql_statements.append("")
        
        sql_statements.append("COMMIT;")
        sql_statements.append("")
        
        # Dodaj zapytania weryfikacyjne
        sql_statements.append("-- Zapytania weryfikacyjne:")
        sql_statements.append("-- SELECT COUNT(*) as updated_records FROM `stg_geodir_gd_place_detail` WHERE `latitude` IS NOT NULL AND `longitude` IS NOT NULL;")
        sql_statements.append("-- SELECT MIN(`latitude`) as min_lat, MAX(`latitude`) as max_lat, MIN(`longitude`) as min_lon, MAX(`longitude`) as max_lon FROM `stg_geodir_gd_place_detail` WHERE `latitude` IS NOT NULL;")
        
        # Zapisz do pliku lub wyświetl
        sql_content = "\n".join(sql_statements)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(sql_content)
            print(f"Zapytania SQL zapisane do pliku: {output_file}")
        else:
            print("\n" + "="*80)
            print("WYGENEROWANE WSADOWE ZAPYTANIA SQL:")
            print("="*80)
            print(sql_content[:2000] + "..." if len(sql_content) > 2000 else sql_content)
        
        # Statystyki
        print(f"\nStatystyki:")
        print(f"- Wygenerowano {total_batches} wsadowych zapytań UPDATE")
        print(f"- Łączna liczba rekordów: {len(valid_coords)}")
        print(f"- Rozmiar wsadu: {batch_size} rekordów")
        print(f"- Tabela docelowa: stg_geodir_gd_place_detail")
        print(f"- Kolumny aktualizowane: longitude, latitude")
        print(f"- Klucz: nip")
        
        return len(valid_coords)
        
    except Exception as e:
        print(f"BŁĄD: {e}")
        return 0

def main():
    """Główna funkcja programu."""
    
    # Domyślne parametry
    input_file = "adresy_geocoded.csv"
    output_file = "update_coordinates_batch.sql"
    batch_size = 100
    
    # Sprawdź argumenty wiersza poleceń
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    if len(sys.argv) > 3:
        try:
            batch_size = int(sys.argv[3])
        except ValueError:
            print("OSTRZEŻENIE: Nieprawidłowy rozmiar wsadu, używam domyślnego: 100")
            batch_size = 100
    
    print("="*70)
    print("GENERATOR WSADOWYCH ZAPYTAŃ SQL UPDATE DLA WSPÓŁRZĘDNYCH")
    print("="*70)
    print(f"Plik wejściowy: {input_file}")
    print(f"Plik wyjściowy: {output_file}")
    print(f"Rozmiar wsadu: {batch_size}")
    print()
    
    # Generuj zapytania
    updated_count = generate_batch_sql_updates(input_file, output_file, batch_size)
    
    if updated_count > 0:
        print(f"\n✅ SUKCES: Wygenerowano zapytania dla {updated_count} rekordów")
        print(f"📁 Plik SQL: {output_file}")
        print("\n⚡ ZALETY WSADOWYCH ZAPYTAŃ:")
        print("- Znacznie szybsze wykonanie niż pojedyncze UPDATE")
        print("- Mniej obciążenia dla bazy danych")
        print("- Lepsze wykorzystanie transakcji")
        print("\n⚠️  UWAGA:")
        print("1. Przed wykonaniem zapytań utwórz kopię zapasową bazy danych")
        print("2. Przetestuj zapytania na środowisku testowym")
        print("3. Sprawdź zapytania weryfikacyjne po wykonaniu UPDATE")
    else:
        print("\n❌ BŁĄD: Nie udało się wygenerować zapytań")

if __name__ == "__main__":
    main()
