-- Test UPDATE z backtickami

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> rekord przed aktualizacją
SELECT `nip`, `latitude`, `longitude`, `city` 
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` = '8711736596';

-- 2. Test UPDATE z backtickami i apostrofami dla varchar
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = '21.0788854', `latitude` = '50.3134211' 
WHERE `nip` = '8711736596';

-- 3. <PERSON><PERSON>wd<PERSON> ile rekordów zostało zaktualizowanych
SELECT ROW_COUNT() as affected_rows;

-- 4. <PERSON><PERSON><PERSON><PERSON><PERSON> rekord po aktualizacji
SELECT `nip`, `latitude`, `longitude`, `city` 
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` = '8711736596';

-- 5. Test z drugim NIP-em
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = '21.0536474', `latitude` = '50.1171886' 
WHERE `nip` = '8711774094';

SELECT ROW_COUNT() as affected_rows_2;
