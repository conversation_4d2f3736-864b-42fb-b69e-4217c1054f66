#!/usr/bin/env python3
"""
Prosty skrypt do poprawiania konkretnych adresów poza powiatem dąbrowskim.
"""

import pandas as pd
import ssl
import certifi
from geopy.geocoders import Nominatim
import time

def extract_city_from_address(address):
    """Wyodrębnia nazwę miejscowości z adresu."""
    return address.split(',')[0].strip()

def geocode_with_dabrowski_focus(geolocator, address):
    """Geokoduje adres z naciskiem na powiat dąbrowski."""
    city = extract_city_from_address(address)
    
    strategies = [
        f"{city}, powiat dąbrowski, małopolskie",
        f"{city}, gmina Dąbrowa Tarnowska, małopolskie",
        f"{city}, gmina Szczucin, małopolskie", 
        f"{city}, gmina Gręboszów, małopolskie",
        f"{city}, gmina Olesno, małopolskie",
        f"{city}, gmina Radgoszcz, małopolskie",
        f"{city}, gmina Bolesław, małopolskie",
        f"{city}, gmina Mędrzechów, małopolskie"
    ]
    
    for i, query in enumerate(strategies, 1):
        try:
            print(f"  Próba {i}: '{query}'")
            location = geolocator.geocode(query, timeout=10)
            
            if location and "powiat dąbrowski" in location.address.lower():
                print(f"  ✅ Znaleziono: {location.address}")
                return location.latitude, location.longitude
            
            time.sleep(1)
            
        except Exception as e:
            print(f"  ❌ Błąd: {e}")
            time.sleep(2)
    
    return None, None

def main():
    # Lista adresów do poprawienia (na podstawie analizy współrzędnych)
    # Adresy z współrzędnymi poza powiatem dąbrowskim
    addresses_to_fix = [
        # Wielopole - adresy z współrzędnymi ~49.6 (powiat nowosądecki)
        "Wielopole,Wielopole 149",  # 49.6578044, 20.6967619
        "Wielopole,Wielopole 49",   # 49.6696066, 20.7107392
        "Wielopole,Wielopole 72",   # 49.6568554, 20.6947177
        "Wielopole,Wielopole 69",   # 49.6625916, 20.7008383

        # Dąbrowica - adresy z współrzędnymi ~49.9 (powiat bocheński)
        "Dąbrowica,Dąbrowica 18",   # 49.9338401, 20.3628983
        "Dąbrowica,Dąbrowica 103",  # 49.9391916, 20.3639213
        "Dąbrowica,Dąbrowica 54",   # 49.9354024, 20.3567405

        # Podlipie - wszystkie adresy z współrzędnymi ~50.29, 19.44 (powiat olkuski)
        "Podlipie,Podlipie 32",     # 50.2918937, 19.4484404
        "Podlipie,Podlipie 58",     # 50.2921861, 19.4457483
        "Podlipie,Podlipie 16",     # 50.291721, 19.449601
        "Podlipie,Podlipie 4",      # 50.2916595, 19.4506011
        "Podlipie,Podlipie 24"      # 50.2917496, 19.4491589
    ]
    
    print("=== POPRAWIANIE ADRESÓW POZA POWIATEM DĄBROWSKIM ===")
    print(f"Znaleziono {len(addresses_to_fix)} adresów do poprawienia")
    
    # Inicjalizuj geokoder
    ssl_context = ssl.create_default_context(cafile=certifi.where())
    geolocator = Nominatim(user_agent="fix_dabrowski_addresses", ssl_context=ssl_context)
    
    # Wczytaj istniejący plik z wynikami
    geocoded_file = "adresy_geocoded.csv"
    try:
        df = pd.read_csv(geocoded_file, sep=';')
        print(f"Wczytano {len(df)} wierszy z pliku {geocoded_file}")
    except Exception as e:
        print(f"Błąd wczytywania pliku: {e}")
        return
    
    # Utwórz kopię zapasową
    backup_file = geocoded_file.replace('.csv', '_backup_before_fix.csv')
    df.to_csv(backup_file, sep=';', index=False)
    print(f"Utworzono kopię zapasową: {backup_file}")
    
    successful_fixes = 0
    
    # Przetwarzaj każdy adres
    for i, address in enumerate(addresses_to_fix, 1):
        print(f"\n[{i}/{len(addresses_to_fix)}] Poprawianie: {address}")
        
        # Znajdź wszystkie wiersze z tym adresem
        mask = df['adres'] == address
        matching_rows = df[mask]
        
        if len(matching_rows) == 0:
            print(f"  ❌ Nie znaleziono adresu '{address}' w pliku")
            continue
        
        print(f"  Znaleziono {len(matching_rows)} wierszy z tym adresem")
        
        # Spróbuj geokodować z naciskiem na powiat dąbrowski
        new_lat, new_lon = geocode_with_dabrowski_focus(geolocator, address)
        
        if new_lat and new_lon:
            # Aktualizuj wszystkie wiersze z tym adresem
            df.loc[mask, 'latitude'] = new_lat
            df.loc[mask, 'longitude'] = new_lon
            successful_fixes += len(matching_rows)
            print(f"  ✅ Zaktualizowano {len(matching_rows)} wierszy")
        else:
            print(f"  ❌ Nie udało się poprawić adresu")
        
        time.sleep(1)  # Opóźnienie między adresami
    
    # Zapisz zaktualizowane dane
    df.to_csv(geocoded_file, sep=';', index=False)
    print(f"\n=== PODSUMOWANIE ===")
    print(f"Pomyślnie poprawiono {successful_fixes} wierszy")
    print(f"Zaktualizowano plik: {geocoded_file}")
    print(f"Kopia zapasowa: {backup_file}")

if __name__ == "__main__":
    main()
