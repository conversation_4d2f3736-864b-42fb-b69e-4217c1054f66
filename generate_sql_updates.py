#!/usr/bin/env python3
"""
Skrypt do generowania zapytań SQL UPDATE na podstawie pliku CSV z wynikami geokodowania.
Aktualizuje kolumny longitude i latitude w tabeli stg_geodir_gd_place_detail.
"""

import pandas as pd
import sys

def generate_sql_updates(csv_file, output_file=None):
    """
    Generuje zapytania SQL UPDATE na podstawie pliku CSV.
    
    Args:
        csv_file: Ścieżka do pliku CSV z wynikami geokodowania
        output_file: Ścieżka do pliku wyjściowego SQL (opcjonalne)
    """
    
    try:
        # Wczytaj plik CSV
        print(f"Wczytywanie pliku: {csv_file}")
        df = pd.read_csv(csv_file, sep=';')
        print(f"Wczytano {len(df)} wierszy")
        
        # Sprawdź wymagane kolumny
        required_columns = ['id', 'latitude', 'longitude']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"BŁĄD: Brakuje kolumn: {missing_columns}")
            return
        
        # Filtruj tylko wiersze z prawidłowymi współrzędnymi
        valid_coords = df.dropna(subset=['latitude', 'longitude'])
        print(f"Znaleziono {len(valid_coords)} wierszy z prawidłowymi współrzędnymi")
        
        if len(valid_coords) == 0:
            print("BŁĄD: Brak wierszy z prawidłowymi współrzędnymi")
            return
        
        # Generuj zapytania SQL UPDATE
        sql_statements = []
        
        # Nagłówek
        sql_statements.append("-- Zapytania UPDATE do aktualizacji współrzędnych geograficznych")
        sql_statements.append("-- Wygenerowane automatycznie na podstawie wyników geokodowania")
        sql_statements.append(f"-- Źródło: {csv_file}")
        sql_statements.append(f"-- Data: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sql_statements.append(f"-- Liczba rekordów do aktualizacji: {len(valid_coords)}")
        sql_statements.append("")
        
        # Rozpocznij transakcję
        sql_statements.append("START TRANSACTION;")
        sql_statements.append("")
        
        # Generuj UPDATE dla każdego wiersza
        for index, row in valid_coords.iterrows():
            nip = row['id']
            latitude = row['latitude']
            longitude = row['longitude']
            
            # Sprawdź, czy wartości są liczbami
            try:
                lat_float = float(latitude)
                lon_float = float(longitude)
            except (ValueError, TypeError):
                print(f"OSTRZEŻENIE: Nieprawidłowe współrzędne dla NIP {nip}: lat={latitude}, lon={longitude}")
                continue
            
            # Generuj zapytanie UPDATE
            update_sql = f"UPDATE `stg_geodir_gd_place_detail` SET `longitude` = {lon_float}, `latitude` = {lat_float} WHERE `nip` = '{nip}';"
            sql_statements.append(update_sql)
        
        sql_statements.append("")
        sql_statements.append("COMMIT;")
        sql_statements.append("")
        
        # Dodaj zapytanie weryfikacyjne
        sql_statements.append("-- Zapytanie weryfikacyjne:")
        sql_statements.append("-- SELECT COUNT(*) as updated_records FROM `stg_geodir_gd_place_detail` WHERE `latitude` IS NOT NULL AND `longitude` IS NOT NULL;")
        
        # Zapisz do pliku lub wyświetl
        sql_content = "\n".join(sql_statements)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(sql_content)
            print(f"Zapytania SQL zapisane do pliku: {output_file}")
        else:
            print("\n" + "="*80)
            print("WYGENEROWANE ZAPYTANIA SQL:")
            print("="*80)
            print(sql_content)
        
        # Statystyki
        print(f"\nStatystyki:")
        print(f"- Wygenerowano {len(valid_coords)} zapytań UPDATE")
        print(f"- Tabela docelowa: stg_geodir_gd_place_detail")
        print(f"- Kolumny aktualizowane: longitude, latitude")
        print(f"- Klucz: nip")
        
        return len(valid_coords)
        
    except Exception as e:
        print(f"BŁĄD: {e}")
        return 0

def main():
    """Główna funkcja programu."""
    
    # Domyślny plik wejściowy
    input_file = "adresy_geocoded.csv"
    output_file = "update_coordinates.sql"
    
    # Sprawdź argumenty wiersza poleceń
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    print("="*60)
    print("GENERATOR ZAPYTAŃ SQL UPDATE DLA WSPÓŁRZĘDNYCH")
    print("="*60)
    print(f"Plik wejściowy: {input_file}")
    print(f"Plik wyjściowy: {output_file}")
    print()
    
    # Generuj zapytania
    updated_count = generate_sql_updates(input_file, output_file)
    
    if updated_count > 0:
        print(f"\n✅ SUKCES: Wygenerowano {updated_count} zapytań UPDATE")
        print(f"📁 Plik SQL: {output_file}")
        print("\n⚠️  UWAGA:")
        print("1. Przed wykonaniem zapytań utwórz kopię zapasową bazy danych")
        print("2. Przetestuj zapytania na środowisku testowym")
        print("3. Sprawdź zapytanie weryfikacyjne po wykonaniu UPDATE")
    else:
        print("\n❌ BŁĄD: Nie udało się wygenerować zapytań")

if __name__ == "__main__":
    main()
