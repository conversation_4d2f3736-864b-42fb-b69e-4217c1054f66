#!/usr/bin/env python3
"""
Test skryptu do poprawiania adresów poza powiatem dąbrowskim.
"""

import ssl
import certifi
from geopy.geocoders import Nominatim
import time

def extract_city_from_address(address):
    """Wyodrębnia nazwę miejscowości z adresu."""
    city = address.split(',')[0].strip()
    return city

def geocode_with_dabrowski_focus(geolocator, address):
    """Geokoduje adres z naciskiem na powiat dąbrowski."""
    city = extract_city_from_address(address)
    
    strategies = [
        # Samą miejscowość + gminy powiatu dąbrowskiego
        f"{city}, powiat dąbrowski, małopolskie",
        f"{city}, gmina Dąbrowa Tarnowska, małopolskie",
        f"{city}, gmina Szczucin, małopolskie", 
        f"{city}, gmina Gręboszów, małopolskie",
        f"{city}, gmina Olesno, małopolskie",
        f"{city}, gmina Radgoszcz, małopolskie",
        f"{city}, gmina Bolesław, małopolskie",
        f"{city}, gmina Mędrzechów, małopolskie",
        f"{city}, Dąbrowa Tarnowska, małopolskie",
        f"{city}, małopolskie"
    ]
    
    print(f"\nTestowanie adresu: {address}")
    print(f"Miejscowość: {city}")
    
    for i, query in enumerate(strategies, 1):
        try:
            print(f"Próba {i}: '{query}'")
            location = geolocator.geocode(query, timeout=10)
            
            if location:
                if "powiat dąbrowski" in location.address.lower():
                    print(f"✅ SUKCES - Znaleziono w powiecie dąbrowskim:")
                    print(f"   Adres: {location.address}")
                    print(f"   Współrzędne: {location.latitude}, {location.longitude}")
                    return location.latitude, location.longitude, location.address
                else:
                    print(f"❌ Znaleziono, ale nie w powiecie dąbrowskim: {location.address}")
            else:
                print(f"❌ Nie znaleziono")
            
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Błąd: {e}")
            time.sleep(2)
    
    print(f"❌ NIEPOWODZENIE - Nie znaleziono {address} w powiecie dąbrowskim")
    return None, None, None

def main():
    # Przykładowe adresy z logów, które były w złych powiatach
    test_addresses = [
        "Wielopole,Wielopole 149",  # był w powiecie nowosądeckim
        "Dąbrowica,Dąbrowica 18",   # był w powiecie bocheńskim  
        "Podlipie,Podlipie 32",     # był w powiecie olkuskim
        "Karsy,Wieś Karsy 5"       # test case
    ]
    
    # Inicjalizuj geokoder
    ssl_context = ssl.create_default_context(cafile=certifi.where())
    geolocator = Nominatim(user_agent="test_fix_powiat", ssl_context=ssl_context)
    
    print("=== TEST POPRAWIANIA ADRESÓW POZA POWIATEM DĄBROWSKIM ===")
    
    successful_fixes = 0
    
    for address in test_addresses:
        result = geocode_with_dabrowski_focus(geolocator, address)
        if result[0] is not None:
            successful_fixes += 1
        print("-" * 80)
    
    print(f"\nPodsumowanie: {successful_fixes}/{len(test_addresses)} adresów poprawiono")

if __name__ == "__main__":
    main()
