-- Debugowanie problemu z NIP

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> czy rekord z tym NIP-em istnieje
SELECT COUNT(*) as count_exists 
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` = '8711736596';

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> dokładną wartość NIP-a (może są ukryte znaki?)
SELECT `nip`, LENGTH(`nip`) as nip_length, HEX(`nip`) as nip_hex
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` LIKE '%8711736596%';

-- 3. <PERSON>prawd<PERSON> czy są podobne NIP-y
SELECT `nip`, `latitude`, `longitude`
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` LIKE '8711736%';

-- 4. <PERSON><PERSON>w<PERSON><PERSON> pierwsze 10 NIP-ów w tabeli
SELECT `nip`, LENGTH(`nip`) as length, `latitude`, `longitude`
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` IS NOT NULL 
LIMIT 10;

-- 5. <PERSON><PERSON><PERSON><PERSON><PERSON> czy są jakieś NIP-y zaczynające się od 871
SELECT COUNT(*) as count_871
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` LIKE '871%';

-- 6. Test z TRIM (może są spacje?)
SELECT COUNT(*) as count_with_trim
FROM `stg_geodir_gd_place_detail` 
WHERE TRIM(`nip`) = '8711736596';

-- 7. Test UPDATE z TRIM
UPDATE `stg_geodir_gd_place_detail` 
SET `longitude` = '21.0788854', `latitude` = '50.3134211' 
WHERE TRIM(`nip`) = '8711736596';

SELECT ROW_COUNT() as affected_rows_with_trim;
