-- Podstawowe sprawdzenie danych

-- 1. <PERSON><PERSON> rekord<PERSON> jest w tabeli?
SELECT COUNT(*) as total_records FROM `stg_geodir_gd_place_detail`;

3137


-- 2. <PERSON><PERSON> rekordów ma niepuste NIP-y?
SELECT COUNT(*) as records_with_nip 
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` IS NOT NULL AND `nip` != '';
3136

-- 3. <PERSON><PERSON><PERSON> 5 rekordów z niepustymi NIP-ami
SELECT `post_id`, `nip`, `regon`, `krs`, `city`, `latitude`, `longitude`
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` IS NOT NULL AND `nip` != ''
LIMIT 5;

post_id
nip
regon
krs
city
latitude
longitude
Full texts
5240
8711707838
12018487200000
0000251265
Szczucin
50.31042385000001
21.056670974090473
Delete Delete
Copy Copy
Edit Edit

5241
8710002656
85001479700000
0000067570
Szczucin
50.31978105
21.07720472407012
Delete Delete
Copy Copy
Edit Edit

5242
8711010965
81371400000
0000178720
Wola Szczucińska
50.3180529
21.1237047
Delete Delete
Copy Copy
Edit Edit

5243
8711782159
52617181700000
0001054112
Szczucin
50.31042385000001
21.056670974090473
Delete Delete
Copy Copy
Edit Edit

5244
8711772853
36752876800000
0000680959
Szczucin
50.31042385000001
21.056670974090473
Delete Delete
Copy Copy
Edit Edit



-- 4. Sprawdź czy może dane są w kolumnie REGON zamiast NIP?
SELECT COUNT(*) as records_with_regon 
FROM `stg_geodir_gd_place_detail` 
WHERE `regon` IS NOT NULL AND `regon` != '';


records_with_regon
Full texts
204


-- 5. Pokaż przykładowe REGON-y
SELECT `regon`, `nip`, `city`
FROM `stg_geodir_gd_place_detail` 
WHERE `regon` IS NOT NULL AND `regon` != ''
LIMIT 5;


regon
nip
city
Full texts
12018487200000
8711707838
Szczucin
Delete Delete
Copy Copy
Edit Edit

85001479700000
8710002656
Szczucin
Delete Delete
Copy Copy
Edit Edit

81371400000
8711010965
Wola Szczucińska
Delete Delete
Copy Copy
Edit Edit

52617181700000
8711782159
Szczucin
Delete Delete
Copy Copy
Edit Edit

36752876800000
8711772853
Szczucin
Delete Delete
Copy Copy
Edit Edit



-- 6. Sprawdź czy nasz NIP jest może w REGON
SELECT COUNT(*) as found_in_regon
FROM `stg_geodir_gd_place_detail` 
WHERE `regon` = '8711736596';
found_in_regon
Full texts
0

-- 7. Sprawdź czy nasz NIP jest może w KRS
SELECT COUNT(*) as found_in_krs
FROM `stg_geodir_gd_place_detail` 
WHERE `krs` = '8711736596';

found_in_krs
Full texts
0

-- 8. Sprawdź wszystkie kolumny dla tego konkretnego numeru
SELECT *
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` = '8711736596' 
   OR `regon` = '8711736596' 
   OR `krs` = '8711736596';


post_id
post_title
_search_title
post_status
post_tags
post_category
default_category
featured
featured_image
submit_ip
overall_rating
rating_count
street
street2
city
region
country
zip
latitude
longitude
mapview
mapzoom
claimed
wojewodztwa_uslugi
powiaty_uslugi
package_id
expire_date
nip
krs
regon
zrodlo_danych
logo
zdjecie_w_tle
glowny_kod_pkd
pozostale_kody_pkd
telefon
adres_e_mail
adres_www
facebook
instagram
business_hours
terms_conditions
Full texts
5445
" TRANS STONES" Olga Chrabąszcz
trans stones olga chrabaszcz
publish
,795,
795
0
NULL
***************
0
0
Sadowa 7
Szczucin
województwo małopolskie
Poland
33-230
50.3134211
21.0788854
NULL
NULL
0
małopolskie
powiat dąbrowski
1
0000-00-00
8711736596
CEIDG
4673Z - Sprzedaż hurtowa drewna, materiałów budowl...
"4631Z - Sprzedaż hurtowa owoców i warzyw 4941Z - ...
0
Delete Delete
Copy Copy
Edit Edit

Z zaznaczonymi: