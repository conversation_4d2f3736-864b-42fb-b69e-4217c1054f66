-- S<PERSON><PERSON><PERSON><PERSON><PERSON> czy rekordy już mają współrzędne

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> kilka pierwszych NIP-ów z naszego pliku
SELECT `nip`, `latitude`, `longitude`, `city`
FROM `stg_geodir_gd_place_detail` 
WHERE `nip` IN ('8711736596', '8711774094', '8711150241', '8711771718', '8711005846')
ORDER BY `nip`;

-- 2. Sprawdź ile rekordów ma już ustawione współrzędne
SELECT COUNT(*) as records_with_coordinates
FROM `stg_geodir_gd_place_detail` 
WHERE `latitude` IS NOT NULL AND `latitude` != '' AND `latitude` != '0'
  AND `longitude` IS NOT NULL AND `longitude` != '' AND `longitude` != '0';

-- 3. Sprawdź ile rekordów ma puste współrzędne
SELECT COUNT(*) as records_without_coordinates
FROM `stg_geodir_gd_place_detail` 
WHERE (`latitude` IS NULL OR `latitude` = '' OR `latitude` = '0')
   OR (`longitude` IS NULL OR `longitude` = '' OR `longitude` = '0');

-- 4. Sprawdź przykładowe rekordy bez współrzędnych
SELECT `nip`, `latitude`, `longitude`, `city`, `street`
FROM `stg_geodir_gd_place_detail` 
WHERE (`latitude` IS NULL OR `latitude` = '' OR `latitude` = '0')
   OR (`longitude` IS NULL OR `longitude` = '' OR `longitude` = '0')
LIMIT 5;
